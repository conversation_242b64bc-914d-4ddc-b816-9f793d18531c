# Ignore everything except frontend source
*
!src/frontend/
!docker/
!docker/nginx.conf

# Ignore frontend development files
src/frontend/node_modules/
src/frontend/.next/
src/frontend/out/
src/frontend/dist/
src/frontend/.cache/
src/frontend/.storybook/build/
src/frontend/coverage/
src/frontend/.nyc_output/
src/frontend/.env*.local
src/frontend/*.log
src/frontend/yarn-error.log
src/frontend/.DS_Store

# Ignore other services
!src/frontend/
src/
!src/frontend/

# Keep only necessary files for frontend build
!src/frontend/src/
!src/frontend/package.json
!src/frontend/yarn.lock
!src/frontend/package-lock.json
!src/frontend/tsconfig.json
!src/frontend/vite.config.mjs
!src/frontend/postcss.config.cjs
!src/frontend/.prettierrc.mjs
!src/frontend/index.html
