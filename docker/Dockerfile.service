# Multi-stage build for minimal image size
FROM python:3.12.3-slim AS builder

# Install build dependencies including C++ compiler for google-crc32c
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    build-essential \
    libffi-dev \
    libssl-dev \
    libopenblas-dev \
    liblapack-dev \
    gfortran \
    python3-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install uv for faster dependency resolution
RUN pip install --no-cache-dir uv

# Copy dependency files
COPY pyproject.toml .
COPY uv.lock .

# Install all dependencies using uv with the lock file for consistency
# This avoids dependency resolution issues and uses exact versions
# Use fallback if frozen sync fails due to package name mismatch
RUN uv sync --frozen || (echo "Frozen sync failed, attempting regular sync..." && uv sync)

# List installed packages for debugging using uv
RUN uv pip list | grep -E "(numexpr|numpy)" || echo "numexpr/numpy not found in dependencies"

# Try to reinstall google-crc32c with C extension to avoid the warning
# Use uv to install with specific build options
RUN uv pip install --force-reinstall --no-binary=google-crc32c google-crc32c || echo "google-crc32c installation skipped"

# Verify critical dependencies are installed
RUN /app/.venv/bin/python -c "import numexpr; print('numexpr version:', numexpr.__version__)" || echo "numexpr not available"
RUN /app/.venv/bin/python -c "import numpy; print('numpy version:', numpy.__version__)" || echo "numpy not available"

# Clean up uv cache and temporary files
RUN uv cache clean && \
    rm -rf /tmp/* /var/tmp/* /root/.cache

# Runtime stage - minimal slim image
FROM python:3.12.3-slim AS runtime

# Install only essential runtime dependencies
RUN apt-get update && apt-get install -y \
    libffi8 \
    libssl3 \
    libopenblas0 \
    liblapack3 \
    libgfortran5 \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy Python packages from builder (uv creates a virtual environment)
COPY --from=builder /app/.venv /app/.venv

# Copy only necessary source code
COPY src/agents/ ./agents/
COPY src/core/ ./core/
COPY src/memory/ ./memory/
COPY src/schema/ ./schema/
COPY src/service/ ./service/
COPY src/run_service.py .

# Copy startup script and patches
COPY docker/start_service.py .
COPY docker/core_init_patch.py ./core/__init__.py

# Create empty environment file (environment variables will be set via Docker)
RUN touch .env

# Make startup script executable
RUN chmod +x start_service.py

# Set environment variables for optimization
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV PYTHONOPTIMIZE=2
ENV PATH="/app/.venv/bin:$PATH"

# Disable ChromaDB telemetry to avoid OpenTelemetry conflicts
ENV ANONYMIZED_TELEMETRY=false
ENV CHROMA_TELEMETRY_DISABLED=true

# Disable OpenTelemetry completely to avoid conflicts
ENV OTEL_SDK_DISABLED=true
ENV OTEL_PYTHON_DISABLED=true
ENV OTEL_TRACES_EXPORTER=none
ENV OTEL_METRICS_EXPORTER=none
ENV OTEL_LOGS_EXPORTER=none

# Set default environment variables for container deployment
ENV HOST=0.0.0.0
ENV PORT=8000
ENV USE_FAKE_MODEL=true
ENV DATABASE_TYPE=sqlite
ENV SQLITE_DB_PATH=/app/checkpoints.db

# Clean up Python cache and optimize
RUN find . -type d -name __pycache__ -exec rm -rf {} + 2>/dev/null || true && \
    find . -name "*.pyc" -delete && \
    find . -name "*.pyo" -delete && \
    find /app/.venv/lib/python3.12/site-packages -name "*.pyc" -delete 2>/dev/null || true && \
    find /app/.venv/lib/python3.12/site-packages -type d -name __pycache__ -exec rm -rf {} + 2>/dev/null || true && \
    find /app/.venv/lib/python3.12/site-packages -name "*.dist-info" -exec rm -rf {} + 2>/dev/null || true && \
    find /app/.venv/lib/python3.12/site-packages -name "tests" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find /app/.venv/lib/python3.12/site-packages -name "test" -type d -exec rm -rf {} + 2>/dev/null || true

# Create non-root user for security
RUN groupadd --gid 1001 appgroup && \
    useradd --uid 1001 --gid appgroup --no-create-home --system appuser

# Change ownership of app directory and virtual environment
RUN chown -R appuser:appgroup /app

USER appuser

# Expose the port that the application will run on
EXPOSE 8000

# Add health check to ensure service starts properly
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Use exec form for better signal handling and startup script
CMD ["python", "-O", "start_service.py"]
