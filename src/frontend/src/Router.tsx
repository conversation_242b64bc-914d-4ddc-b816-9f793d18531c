import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { AppLayout } from './components';
import { HomePage } from './pages/home';
import { DashboardPage } from './pages/dashboard';
import { GoalsPage } from './pages/goals';
import { LearningPathsPage } from './pages/learningPaths';
import { CoursesPage } from './pages/courses';
import { ProgressPage } from './pages/progress';

const router = createBrowserRouter([
  {
    path: '/',
    element: <AppLayout><HomePage /></AppLayout>,
  },
  {
    path: '/dashboard',
    element: <AppLayout><DashboardPage /></AppLayout>,
  },
  {
    path: '/goals',
    element: <AppLayout><GoalsPage /></AppLayout>,
  },
  {
    path: '/learning-paths',
    element: <AppLayout><LearningPathsPage /></AppLayout>,
  },
  {
    path: '/courses',
    element: <AppLayout><CoursesPage /></AppLayout>,
  },
  {
    path: '/progress',
    element: <AppLayout><ProgressPage /></AppLayout>,
  },
]);

export function Router() {
  return <RouterProvider router={router} />;
}
