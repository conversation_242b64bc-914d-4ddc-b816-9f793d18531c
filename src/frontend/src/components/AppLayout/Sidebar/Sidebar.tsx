import { ScrollArea } from '@mantine/core';
import { NavigationSection } from './NavigationSection/NavigationSection';
import { GoalHistorySection } from './GoalHistorySection/GoalHistorySection';
import { SkillsSection } from './SkillsSection/SkillsSection';
import { SettingsSection } from './SettingsSection/SettingsSection';
import classes from './Sidebar.module.css';

export function Sidebar() {
  return (
    <ScrollArea className={classes.sidebar} h="100vh">
      <div className={classes.content}>
        {/* Navigation Section */}
        <div className={classes.section}>
          <NavigationSection />
        </div>

        {/* Goal History Section - This section can expand */}
        <div className={classes.section}>
          <GoalHistorySection />
        </div>

        {/* Skills Section */}
        <div className={classes.section}>
          <SkillsSection />
        </div>

        {/* Settings Section */}
        <div className={classes.section}>
          <SettingsSection />
        </div>
      </div>
    </ScrollArea>
  );
}
