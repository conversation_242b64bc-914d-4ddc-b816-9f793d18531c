.item {
  display: block;
  width: 100%;
  padding: var(--mantine-spacing-sm);
  border-radius: var(--mantine-radius-sm);
  text-decoration: none;
  color: var(--mantine-color-gray-7);
  transition: background-color 0.2s ease;
}

.item:hover {
  background-color: var(--mantine-color-gray-1);
}

.icon {
  color: var(--mantine-color-gray-6);
  transition: color 0.2s ease;
}

.label {
  color: inherit;
}

@media (prefers-color-scheme: dark) {
  .item {
    color: var(--mantine-color-dark-0);
  }

  .item:hover {
    background-color: var(--mantine-color-dark-6);
  }

  .icon {
    color: var(--mantine-color-dark-2);
  }
}
