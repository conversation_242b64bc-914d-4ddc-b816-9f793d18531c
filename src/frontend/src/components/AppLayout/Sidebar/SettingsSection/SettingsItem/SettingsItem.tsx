import { UnstyledButton, Group, Text } from '@mantine/core';
import { Link } from 'react-router-dom';
import classes from './SettingsItem.module.css';

interface SettingsItemProps {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  href: string;
}

export function SettingsItem({ icon: Icon, label, href }: SettingsItemProps) {
  return (
    <UnstyledButton
      component={Link}
      to={href}
      className={classes.item}
    >
      <Group gap="sm">
        <Icon size={20} className={classes.icon} />
        <Text size="sm" className={classes.label}>
          {label}
        </Text>
      </Group>
    </UnstyledButton>
  );
}
