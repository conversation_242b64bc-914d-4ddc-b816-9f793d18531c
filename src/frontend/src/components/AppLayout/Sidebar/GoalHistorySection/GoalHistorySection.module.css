.sectionTitle {
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 11px;
  font-weight: 600;
}

.goalItem {
  background-color: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-gray-2);
  border-radius: var(--mantine-radius-sm);
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.goalItem:hover {
  background-color: var(--mantine-color-gray-1);
}

@media (prefers-color-scheme: dark) {
  .goalItem {
    background-color: var(--mantine-color-dark-7);
    border: 1px solid var(--mantine-color-dark-4);
  }

  .goalItem:hover {
    background-color: var(--mantine-color-dark-6);
  }
}
