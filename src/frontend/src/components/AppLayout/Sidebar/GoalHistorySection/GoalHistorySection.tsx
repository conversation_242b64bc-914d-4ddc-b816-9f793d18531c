import { Stack, Text, Paper } from '@mantine/core';
import classes from './GoalHistorySection.module.css';

// Demo content to test scrolling - this section is empty in the original design
const demoGoals = [
  'Learn React fundamentals',
  'Master TypeScript',
  'Build a portfolio website',
  'Learn Node.js backend',
  'Study database design',
  'Practice algorithm problems',
  'Learn Docker basics',
  'Understand CI/CD pipelines',
  'Study system design',
  'Learn cloud computing',
];

export function GoalHistorySection() {
  return (
    <Stack gap="md">
      <Text size="sm" fw={600} c="dimmed" className={classes.sectionTitle}>
        RECENT GOALS (Demo)
      </Text>
      <Stack gap="xs">
        {demoGoals.map((goal, index) => (
          <Paper key={index} p="xs" className={classes.goalItem}>
            <Text size="xs" c="dimmed">
              {goal}
            </Text>
          </Paper>
        ))}
      </Stack>
    </Stack>
  );
}
