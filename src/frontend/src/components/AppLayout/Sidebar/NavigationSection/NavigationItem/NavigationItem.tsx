import { UnstyledButton, Group, Text } from '@mantine/core';
import { Link } from 'react-router-dom';
import classes from './NavigationItem.module.css';

interface NavigationItemProps {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  href: string;
  active?: boolean;
}

export function NavigationItem({ icon: Icon, label, href, active }: NavigationItemProps) {
  return (
    <UnstyledButton
      component={Link}
      to={href}
      className={`${classes.item} ${active ? classes.active : ''}`}
    >
      <Group gap="sm">
        <Icon size={20} className={classes.icon} />
        <Text size="sm" fw={500} className={classes.label}>
          {label}
        </Text>
      </Group>
    </UnstyledButton>
  );
}
