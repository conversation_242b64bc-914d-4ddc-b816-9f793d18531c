.item {
  display: block;
  width: 100%;
  padding: var(--mantine-spacing-sm);
  border-radius: var(--mantine-radius-sm);
  text-decoration: none;
  color: var(--mantine-color-gray-7);
  transition: background-color 0.2s ease;
}

.item:hover {
  background-color: var(--mantine-color-gray-1);
}

.item.active {
  background-color: var(--mantine-color-blue-0);
  color: var(--mantine-color-blue-7);
}

.item.active .icon {
  color: var(--mantine-color-blue-6);
}

.icon {
  color: var(--mantine-color-gray-6);
  transition: color 0.2s ease;
}

.label {
  color: inherit;
}

@media (prefers-color-scheme: dark) {
  .item {
    color: var(--mantine-color-dark-0);
  }

  .item:hover {
    background-color: var(--mantine-color-dark-6);
  }

  .item.active {
    background-color: var(--mantine-color-blue-9);
    color: var(--mantine-color-blue-2);
  }

  .item.active .icon {
    color: var(--mantine-color-blue-4);
  }

  .icon {
    color: var(--mantine-color-dark-2);
  }
}
