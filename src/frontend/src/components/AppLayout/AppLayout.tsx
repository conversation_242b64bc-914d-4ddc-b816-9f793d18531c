import { AppShell } from '@mantine/core';
import { ReactNode } from 'react';
import { Sidebar } from './Sidebar/Sidebar';
import classes from './AppLayout.module.css';

interface AppLayoutProps {
  children: ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  return (
    <AppShell
      navbar={{
        width: 280,
        breakpoint: 'sm',
      }}
      padding="md"
      className={classes.shell}
    >
      <AppShell.Navbar className={classes.navbar}>
        <Sidebar />
      </AppShell.Navbar>

      <AppShell.Main className={classes.main}>
        {children}
      </AppShell.Main>
    </AppShell>
  );
}
