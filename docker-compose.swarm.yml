version: '3.8'

services:
  pathforge_ai_agent_service:
    image: dockerhub.csharpp.com/pathforge_ai/agent_service:${VERSION:-latest}
    environment:
      # Add your environment variables here
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-}
      - LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-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}
      - OPENROUTER_MODEL=${OPENROUTER_MODEL:-qwen/qwen3-235b-a22b}
      - OPENROUTER_BASEURL=${OPENROUTER_BASEURL:-https://openrouter.ai/api/v1}
      - OPENWEATHERMAP_API_KEY=${OPENWEATHERMAP_API_KEY:-********************************}
      - BRAVE_SEARCH_API_KEY=${BRAVE_SEARCH_API_KEY:-BSAm3V_RwMeOMpifscQLjSfMj5y034x}
      - DEFAULT_MODEL=${DEFAULT_MODEL:-openrouter}
    networks:
      internal:
      traefik_main:
    extra_hosts:
      - "dockerhub.csharpp.com:***********"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: pause
        order: stop-first
      # resources:
      #   limits:
      #     memory: 2G
      #     cpus: '1.0'
      #   reservations:
      #     memory: 1G
      #     cpus: '0.5'
      labels:
        # Traefik configuration
        - traefik.constraint-label=traefik_main
        - "traefik.enable=true"
        - "traefik.docker.network=traefik_main"

        # HTTP router
        - "traefik.http.routers.pathforge_ai-agent-service.rule=Host(`pathforge-ai-backend.csharpp.com`)"
        - "traefik.http.routers.pathforge_ai-agent-service.entrypoints=https"
        - "traefik.http.routers.pathforge_ai-agent-service.tls=true"
        - "traefik.http.routers.pathforge_ai-agent-service.tls.certresolver=le"

        # Service configuration
        - "traefik.http.services.pathforge_ai-agent-service.loadbalancer.server.port=8000"
        - traefik.http.routers.pathforge_ai-agent-service.service=pathforge_ai-agent-service
        
  pathforge_ai_streamlit_app:
    image: dockerhub.csharpp.com/pathforge_ai/streamlit_app:${VERSION:-latest}
    environment:
      # - AGENT_URL=https://pathforge-ai-backend.csharpp.com
      - AGENT_URL=http://pathforge_ai_agent_service:8000
      # - OPENAI_API_KEY=${OPENAI_API_KEY}
    networks:
      internal:
      traefik_main:
    extra_hosts:
      - "dockerhub.csharpp.com:***********"
      - "pathforge-ai-backend.csharpp.com:***********"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: pause
        order: stop-first
      # resources:
      #   limits:
      #     memory: 1G
      #     cpus: '0.5'
      #   reservations:
      #     memory: 512M
      #     cpus: '0.25'
      labels:
        # Traefik configuration
        - traefik.constraint-label=traefik_main
        - "traefik.enable=true"
        - "traefik.docker.network=traefik_main"

        # HTTP router
        - "traefik.http.routers.pathforge_ai-streamlit-app.rule=Host(`pathforge_ai-streamlit.csharpp.com`)"
        - "traefik.http.routers.pathforge_ai-streamlit-app.entrypoints=https"
        - "traefik.http.routers.pathforge_ai-streamlit-app.tls=true"
        - "traefik.http.routers.pathforge_ai-streamlit-app.tls.certresolver=le"

        # Service configuration
        - "traefik.http.services.pathforge_ai-streamlit-app.loadbalancer.server.port=8501"
        - traefik.http.routers.pathforge_ai-streamlit-app.service=pathforge_ai-streamlit-app
        - traefik.http.routers.pathforge_ai-streamlit-app.middlewares=pathforge_ai-streamlit-app-basic-auth
        # Basic auth  
        # - traefik.http.middlewares.https-redirect.redirectscheme.scheme=https
        - traefik.http.middlewares.pathforge_ai-streamlit-app-basic-auth.basicauth.users=dcs:$$apr1$$rFShrRLe$$jxNtOI.1SLt7F8EQ7PPMs. # j2CcsOsuqrT7Lbgr
        - traefik.http.middlewares.pathforge_ai-streamlit-app-basic-auth.basicauth.removeheader=true
        # - traefik.http.middlewares.sslheader.headers.customrequestheaders.X-Forwarded-Proto=https

  pathforge_ai_frontend:
    image: dockerhub.csharpp.com/pathforge_ai/frontend:${VERSION:-latest}
    environment:
      - REACT_APP_API_URL=https://pathforge-ai-backend.csharpp.com
    networks:
      internal:
      traefik_main:
    extra_hosts:
      - "dockerhub.csharpp.com:***********"
      - "pathforge-ai-backend.csharpp.com:***********"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: pause
        order: stop-first
      # resources:
      #   limits:
      #     memory: 512M
      #     cpus: '0.25'
      #   reservations:
      #     memory: 256M
      #     cpus: '0.1'
      labels:
        # Traefik configuration
        - traefik.constraint-label=traefik_main
        - "traefik.enable=true"
        - "traefik.docker.network=traefik_main"

        # HTTP router
        - "traefik.http.routers.pathforge_ai-frontend.rule=Host(`pathforge-ai.csharpp.com`)"
        - "traefik.http.routers.pathforge_ai-frontend.entrypoints=https"
        - "traefik.http.routers.pathforge_ai-frontend.tls=true"
        - "traefik.http.routers.pathforge_ai-frontend.tls.certresolver=le"

        # Service configuration
        - "traefik.http.services.pathforge_ai-frontend.loadbalancer.server.port=80"
        - traefik.http.routers.pathforge_ai-frontend.service=pathforge_ai-frontend

networks:
  internal:
    driver: overlay
    internal: true
  traefik_main:
    external: true
