name: 'PathForge AI - Create Test Environment'
description: 'Create test environment file with required secrets for PathForge AI'

inputs:
  brave-search-api-key:
    description: 'Brave Search API Key'
    required: true
  openweathermap-api-key:
    description: 'OpenWeatherMap API Key'
    required: true
  openrouter-api-key:
    description: 'OpenRouter API Key'
    required: true
  openrouter-model:
    description: 'OpenRouter Model'
    required: true
  openrouter-baseurl:
    description: 'OpenRouter Base URL'
    required: true

runs:
  using: 'composite'
  steps:
  - name: Create test environment file
    shell: bash
    run: |
      cat > .env << EOF
      OPENAI_API_KEY=sk-fake-openai-key
      POSTGRES_USER=postgres
      POSTGRES_PASSWORD=postgres
      POSTGRES_DB=agent_service
      BRAVE_SEARCH_API_KEY=${{ inputs.brave-search-api-key }}
      OPENWEATHERMAP_API_KEY=${{ inputs.openweathermap-api-key }}
      OPENROUTER_API_KEY=${{ inputs.openrouter-api-key }}
      OPENROUTER_MODEL=${{ inputs.openrouter-model }}
      OPENROUTER_BASEURL=${{ inputs.openrouter-baseurl }}
      EOF
