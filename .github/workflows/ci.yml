name: PathForge AI - CI Pipeline

on:
  push:
    branches: [ develop ]
  workflow_dispatch:

env:
  PYTHON_VERSION: "3.12"
  UV_VERSION: "0.5.11"

jobs:
  # Check what files have changed
  changes:
    uses: ./.github/workflows/reusable-changes.yml
    permissions:
      contents: read

  frontend-test:
    needs: changes
    if: needs.changes.outputs.frontend == 'true'
    uses: ./.github/workflows/reusable-frontend-test.yml
    permissions:
      contents: read
    with:
      node-version: "18"

  frontend-docker-build:
    needs: changes
    if: needs.changes.outputs.frontend == 'true'
    uses: ./.github/workflows/reusable-frontend-docker-build.yml
    permissions:
      contents: read
    with:
      push: false

  code-quality:
    needs: changes
    if: needs.changes.outputs.python == 'true'
    uses: ./.github/workflows/reusable-code-quality.yml
    permissions:
      contents: read
    with:
      python-version: "3.12"
      uv-version: "0.5.11"

  test:
    needs: changes
    if: needs.changes.outputs.python == 'true'
    uses: ./.github/workflows/reusable-test.yml
    permissions:
      contents: read
    with:
      python-version: "3.12"
      uv-version: "0.5.11"
      upload-coverage: true
    secrets:
      OPENWEATHERMAP_API_KEY: ${{ secrets.OPENWEATHERMAP_API_KEY }}
      OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
      OPENROUTER_MODEL: ${{ secrets.OPENROUTER_MODEL }}
      OPENROUTER_BASEURL: ${{ secrets.OPENROUTER_BASEURL }}
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  docker-build:
    needs: changes
    if: needs.changes.outputs.docker == 'true'
    uses: ./.github/workflows/reusable-docker-build.yml
    permissions:
      contents: read
    with:
      push: false

  # docker-test:
  #   needs: [changes, docker-build]
  #   if: needs.changes.outputs.docker == 'true'
  #   uses: ./.github/workflows/reusable-docker-test.yml
  #   permissions:
  #     contents: read
