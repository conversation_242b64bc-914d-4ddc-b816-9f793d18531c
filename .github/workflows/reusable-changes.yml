name: PathForge AI - Detect Changes

on:
  workflow_call:
    outputs:
      python:
        description: 'Python files changed'
        value: ${{ jobs.changes.outputs.python }}
      frontend:
        description: 'Frontend files changed'
        value: ${{ jobs.changes.outputs.frontend }}
      docker:
        description: 'Docker-related files changed'
        value: ${{ jobs.changes.outputs.docker }}

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      python: ${{ steps.changes.outputs.python }}
      frontend: ${{ steps.changes.outputs.frontend }}
      docker: ${{ steps.changes.outputs.docker }}
    steps:
    - uses: actions/checkout@v4
    - uses: dorny/paths-filter@v3
      id: changes
      with:
        filters: |
          python:
            - '**/*.py'
          frontend:
            - 'src/frontend/**'
            - '**/*.json'
            - '**/*.js'
            - '**/*.ts'
            - '**/*.tsx'
            - '**/*.mjs'
            - '**/*.css'
            - '**/*.html'
          docker:
            - '**/*.py'
            - '**/*.yml'
            - '**/*.yaml'
            - '**/Dockerfile*'
            - 'src/frontend/**'
