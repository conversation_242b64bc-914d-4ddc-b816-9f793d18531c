<!-- PathForge AI Pull Request Template -->

## Description

Brief description of the changes in this PR.

## Type of Change

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] CI/CD changes
- [ ] Dependency updates

## Changes Made

- List the main changes made in this PR
- Be specific about what was added, modified, or removed
- Include any new dependencies or configuration changes

## Testing

- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] New tests added for new functionality

### Test Coverage
- [ ] Code coverage maintained or improved
- [ ] All new code is covered by tests

## Documentation

- [ ] README updated (if applicable)
- [ ] API documentation updated (if applicable)
- [ ] Code comments added/updated
- [ ] Changelog updated

## Deployment

- [ ] No deployment changes required
- [ ] Environment variables added/changed (document below)
- [ ] Database migrations required
- [ ] Configuration changes required

### Environment Variables (if applicable)
List any new or changed environment variables:

```
NEW_VAR=example_value
CHANGED_VAR=new_default_value
```

## Breaking Changes

If this is a breaking change, describe:
- What breaks
- How to migrate existing setups
- Deprecation timeline (if applicable)

## Screenshots (if applicable)

Add screenshots to help explain your changes.

## Checklist

- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## Related Issues

Closes #(issue number)
Relates to #(issue number)
