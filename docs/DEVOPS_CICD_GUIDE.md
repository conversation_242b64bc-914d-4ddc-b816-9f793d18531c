# PathForge AI - DevOps & CI/CD Guide

This comprehensive guide covers all aspects of DevOps and CI/CD for PathForge AI, including setup, optimization, deployment, and best practices.

## Table of Contents

1. [CI/CD Pipeline Overview](#cicd-pipeline-overview)
2. [Docker Optimization](#docker-optimization)
3. [Docker Swarm Deployment](#docker-swarm-deployment)
4. [CI/CD Pipeline Refactoring](#cicd-pipeline-refactoring)
5. [Troubleshooting](#troubleshooting)
6. [Best Practices](#best-practices)

---

## CI/CD Pipeline Overview

PathForge AI uses GitHub Actions for CI/CD with the following workflows:

- **CI Pipeline**: Automated testing, code quality checks, and Docker builds
- **CD Pipeline**: Automated deployment to staging and production
- **PR Checks**: Additional validation for pull requests
- **Release Management**: Automated release creation and versioning
- **Maintenance**: Scheduled tasks for security and cleanup

### Prerequisites

#### Required Secrets

Configure the following secrets in your GitHub repository settings:

1. **CODECOV_TOKEN**: Token for code coverage reporting
2. **GITHUB_TOKEN**: Automatically provided by GitHub Actions

#### Optional Secrets (for deployment)

3. **STAGING_HOST**: SSH host for staging deployment
4. **STAGING_USER**: SSH user for staging deployment
5. **STAGING_KEY**: SSH private key for staging deployment
6. **PRODUCTION_HOST**: SSH host for production deployment
7. **PRODUCTION_USER**: SSH user for production deployment
8. **PRODUCTION_KEY**: SSH private key for production deployment

### Workflow Details

#### CI Pipeline (`.github/workflows/ci.yml`)

**Triggers**: Push to develop, Manual dispatch

**Jobs**:
1. **Test**: Runs unit tests, integration tests, and code quality checks
2. **Docker Build**: Builds Docker images for both services
3. **Integration Test**: Tests the full application stack

**Features**:
- Comprehensive code quality checks with auto-fixing
- Code coverage reporting with Codecov
- Parallel Docker builds for efficiency
- Unit testing without external dependencies

#### CD Pipeline (`.github/workflows/cd.yml`)

**Triggers**: Git tags, Manual dispatch

**Jobs**:
1. **Build and Push**: Builds and pushes Docker images to GitHub Container Registry
2. **Deploy Staging**: Deploys to staging environment (manual dispatch)
3. **Deploy Production**: Deploys to production environment (tags only)
4. **Security Scan**: Scans Docker images for vulnerabilities

**Features**:
- Multi-environment support
- Container registry integration
- Security scanning with Trivy
- Environment-specific configurations

#### PR Checks (`.github/workflows/pr.yml`)

**Triggers**: Pull request events

**Jobs**:
1. **PR Validation**: Validates PR title format
2. **Wait for Copilot**: Waits for GitHub Copilot response before allowing merge (10-minute timeout)
3. **Dependency Check**: Checks for security vulnerabilities
4. **Size Check**: Reports Docker image sizes
5. **Performance Test**: Basic performance validation

**GitHub Copilot Integration**:
- Automatically waits for GitHub Copilot to review PRs
- 10-minute timeout with fallback to manual review
- Adds warning comment if Copilot doesn't respond

#### Release Management (`.github/workflows/release.yml`)

**Triggers**: Git tags, Manual dispatch

**Features**:
- Automatic changelog generation
- Release notes with Docker image information
- Version bumping in pyproject.toml
- Pre-release detection

#### Maintenance (`.github/workflows/maintenance.yml`)

**Triggers**: Weekly schedule, Manual dispatch

**Jobs**:
1. **Cleanup Packages**: Removes old container images
2. **Security Audit**: Runs security scans
3. **Dependency Updates**: Checks for outdated dependencies
4. **Performance Baseline**: Establishes performance benchmarks

### Docker Images

Images are published to custom Docker registry:

- `dockerhub.csharpp.com/codepluse/agent_service`
- `dockerhub.csharpp.com/codepluse/streamlit_app`

**Tags**:
- `latest`: Latest tagged release
- `v0.x.0`: Semantic version tags (auto-incremented minor versions)

**Versioning**:
- Major version is fixed at 0
- Minor version auto-increments on each release
- Patch version is always 0
- Images are only built on tag creation events

---

## Docker Optimization

The Streamlit app Docker image has been optimized using a multi-stage Alpine Linux build approach, significantly reducing the final image size.

### Optimization Strategies

#### 1. Multi-Stage Build

**Before**: Single-stage build with `python:3.12.3-slim`
**After**: Multi-stage build with `python:3.12.3-alpine`

Benefits:
- Separates build dependencies from runtime dependencies
- Only includes necessary files in the final image
- Reduces attack surface by excluding build tools

#### 2. Alpine Linux Base Image

**Before**: Debian slim (~120MB base)
**After**: Alpine Linux (~50MB base)

Benefits:
- Significantly smaller base image
- Security-focused distribution
- Minimal package footprint

#### 3. Minimal Dependencies

The Streamlit app only requires:
- `streamlit` - Web framework
- `httpx` - HTTP client for API communication
- `pydantic` - Data validation
- `python-dotenv` - Environment variable management

#### 4. Build Optimizations

- **Bytecode compilation**: `PYTHONOPTIMIZE=2` for smaller bytecode
- **No cache**: `--no-cache-dir` to avoid storing pip cache
- **Dependency grouping**: Uses `uv` with `--only-group client` for minimal installs
- **Layer optimization**: Combines commands to reduce layers

#### 5. Runtime Optimizations

- **Non-root user**: Security best practice
- **Streamlit-specific environment variables**: Disable unnecessary features
- **Health checks**: Built-in container health monitoring
- **Signal handling**: Proper process management with exec form

### Environment Variables

The following environment variables are set for optimization:

```dockerfile
# Python optimizations
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV PYTHONOPTIMIZE=2

# Streamlit optimizations
ENV STREAMLIT_SERVER_HEADLESS=true
ENV STREAMLIT_SERVER_ENABLE_CORS=false
ENV STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION=false
ENV STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
```

### Security Enhancements

1. **Non-root user**: Runs as `appuser` (UID 1001)
2. **Minimal attack surface**: Only essential packages installed
3. **No build tools in final image**: Build dependencies removed
4. **Health checks**: Enables container orchestration monitoring

### Size Comparison

The optimization reduces image size by approximately:
- **Base image**: ~70MB reduction (Debian slim → Alpine)
- **Dependencies**: ~50-100MB reduction (minimal vs full dependencies)
- **Build artifacts**: ~20-50MB reduction (multi-stage build)

**Estimated total reduction**: 140-220MB

### Usage

#### Building the Optimized Image

```bash
# Build the optimized Streamlit app image
docker build -f docker/Dockerfile.app -t streamlit_app:optimized .

# Check the image size
docker images streamlit_app:optimized
```

#### Running the Optimized Container

```bash
# Run with health checks
docker run -d \
  --name streamlit-app \
  -p 8501:8501 \
  -e AGENT_URL=http://agent-service:8080 \
  streamlit_app:optimized

# Check health status
docker ps --format "table {{.Names}}\t{{.Status}}"
```

---

## Docker Swarm Deployment

This section explains how to deploy the CodePluse Platform using Docker Swarm with Traefik integration.

### Prerequisites

1. **Docker Swarm**: Ensure Docker Swarm is initialized
   ```bash
   docker swarm init
   ```

2. **Traefik**: Have Traefik running with the `traefik_main` network
   ```bash
   docker network create --driver overlay traefik_main
   ```

3. **Domain Names**: Configure DNS to point to your Docker Swarm manager node

### Quick Start

1. **Copy environment file**:
   ```bash
   cp .env.swarm.example .env.production
   ```

2. **Edit configuration**:
   ```bash
   nano .env.production
   ```
   Update the following variables:
   - `AGENT_SERVICE_DOMAIN`: Your API domain (e.g., `api.yourdomain.com`)
   - `STREAMLIT_APP_DOMAIN`: Your app domain (e.g., `app.yourdomain.com`)
   - `POSTGRES_PASSWORD`: Secure database password
   - Add your API keys and other environment variables

3. **Deploy the stack**:
   ```bash
   ./scripts/deploy-swarm.sh v0.2.0 production
   ```

### Configuration

#### Environment Variables

| Variable               | Description                    | Default         |
| ---------------------- | ------------------------------ | --------------- |
| `VERSION`              | Docker image version to deploy | `latest`        |
| `OPENAI_API_KEY`       | OpenAI API key (required)      | -               |
| `AGENT_SERVICE_DOMAIN` | Domain for API service         | `api.localhost` |
| `STREAMLIT_APP_DOMAIN` | Domain for web app             | `app.localhost` |
| `CERT_RESOLVER`        | Traefik certificate resolver   | `letsencrypt`   |
| `ANTHROPIC_API_KEY`    | Anthropic API key (optional)   | -               |
| `LANGSMITH_API_KEY`    | LangSmith API key (optional)   | -               |

#### Service Configuration

The stack includes the following services:

##### Agent Service (API)
- **Replicas**: 2 (for high availability)
- **Resources**: 2GB memory limit, 1 CPU limit
- **Networks**: Internal + Traefik
- **Health checks**: HTTP endpoint `/info`
- **Rolling updates**: Zero-downtime deployments

##### Streamlit App
- **Replicas**: 2 (for high availability)
- **Resources**: 1GB memory limit, 0.5 CPU limit
- **Networks**: Internal + Traefik
- **Health checks**: HTTP endpoint `/healthz`
- **Rolling updates**: Zero-downtime deployments

### Traefik Integration

The stack is configured to work with an existing Traefik instance:

#### Labels Configuration

Each service includes Traefik labels for:
- **Routing**: Host-based routing to your domains
- **TLS**: Automatic SSL certificate management
- **Load Balancing**: Health checks and load distribution
- **Security**: Custom headers and middleware

#### Required Traefik Configuration

Ensure your Traefik instance has:
- Certificate resolver configured (Let's Encrypt recommended)
- Docker provider enabled
- `traefik_main` network attached

### Deployment Commands

#### Deploy New Version
```bash
./scripts/deploy-swarm.sh v0.3.0 production
```

#### Deploy to Staging
```bash
./scripts/deploy-swarm.sh latest staging
```

#### Check Stack Status
```bash
docker stack services codepluse-platform
docker stack ps codepluse-platform
```

#### View Service Logs
```bash
docker service logs codepluse-platform_agent_service
docker service logs codepluse-platform_streamlit_app
```

#### Update Single Service
```bash
docker service update --image dockerhub.csharpp.com/codepluse/agent_service:v0.3.0 codepluse-platform_agent_service
```

### Scaling

#### Scale Services
```bash
# Scale agent service to 3 replicas
docker service scale codepluse-platform_agent_service=3

# Scale streamlit app to 4 replicas
docker service scale codepluse-platform_streamlit_app=4
```

#### Resource Limits
Modify `docker-compose.swarm.yml` to adjust resource limits:
```yaml
deploy:
  resources:
    limits:
      memory: 4G
      cpus: '2.0'
    reservations:
      memory: 2G
      cpus: '1.0'
```

### Monitoring

#### Service Health
```bash
# Check service health
docker service ls
docker service ps codepluse-platform_agent_service --no-trunc
```

#### Resource Usage
```bash
# Monitor resource usage
docker stats $(docker ps --format "{{.Names}}" | grep codepluse-platform)
```

### Legacy Deployment Methods

#### Staging Deployment

Triggered by CD workflow on tag creation or manual dispatch:

```bash
# Manual staging deployment
./scripts/deploy.sh staging main
```

#### Production Deployment

Triggered by creating a git tag:

```bash
# Create and push a release tag
git tag v1.0.0
git push origin v1.0.0

# Manual production deployment
./scripts/deploy.sh production v1.0.0
```

#### Environment Configuration

Create environment-specific files:

- `.env.staging`: Staging environment variables
- `.env.production`: Production environment variables

Use `.env.example` as a template.

#### Portainer Integration

After successful Docker image builds, a webhook is automatically triggered to Portainer for deployment updates.

**Setup**:
1. Configure `PORTAINER_WEBHOOK_URL` secret in GitHub repository
2. Webhook payload includes service info, version, and image URLs
3. Portainer can use this to automatically update stack deployments

---

## CI/CD Pipeline Refactoring

This section summarizes the refactoring of GitHub Actions workflows to eliminate code duplication and improve maintainability by creating reusable components.

### Reusable Components Created

#### 1. Reusable Workflows

##### `.github/workflows/reusable-changes.yml`
- **Purpose**: Detect file changes to conditionally run jobs
- **Outputs**: `python` and `docker` flags
- **Used by**: CI, PR workflows

##### `.github/workflows/reusable-code-quality.yml`
- **Purpose**: Run code quality checks (Ruff, mypy)
- **Inputs**: Python version, UV version
- **Used by**: CI, PR workflows

##### `.github/workflows/reusable-test.yml`
- **Purpose**: Run unit tests with coverage
- **Inputs**: Python version, UV version, upload-coverage flag
- **Secrets**: All required API keys for tests
- **Used by**: CI, PR workflows

##### `.github/workflows/reusable-docker-build.yml`
- **Purpose**: Build and optionally push Docker images
- **Inputs**: push flag, registry, tag-prefix
- **Secrets**: Docker credentials
- **Used by**: CI, PR, CD workflows

##### `.github/workflows/reusable-docker-test.yml`
- **Purpose**: Test Docker images
- **Used by**: CI, PR workflows

#### 2. Composite Actions

##### `.github/actions/setup-python/action.yml`
- **Purpose**: Set up Python environment with UV and dependencies
- **Inputs**: Python version, UV version, install-dev flag
- **Used by**: All workflows requiring Python setup

##### `.github/actions/create-test-env/action.yml`
- **Purpose**: Create test environment file with secrets
- **Inputs**: All required API keys
- **Used by**: Test workflows (can be used in future)

### Workflows Refactored

#### 1. CI Workflow (`.github/workflows/ci.yml`)
**Before**: 181 lines with duplicated setup code
**After**: 51 lines using reusable components

**Changes**:
- Uses `reusable-changes.yml` for file change detection
- Uses `reusable-code-quality.yml` for code quality checks
- Uses `reusable-test.yml` for unit tests
- Uses `reusable-docker-build.yml` and `reusable-docker-test.yml` for Docker operations

#### 2. PR Workflow (`.github/workflows/pr.yml`)
**Before**: 392 lines with significant duplication
**After**: 264 lines using reusable components

**Changes**:
- Uses same reusable workflows as CI
- Maintains PR-specific jobs (pr-validation, dependency-check, size-check)
- Eliminates duplicate Python setup and test code

#### 3. CD Workflow (`.github/workflows/cd.yml`)
**Before**: 193 lines with custom Docker build logic
**After**: 143 lines using reusable Docker build

**Changes**:
- Added `get-version` job to extract version information
- Uses `reusable-docker-build.yml` for building and pushing images
- Updated dependent jobs to use version from `get-version` job

#### 4. Maintenance Workflow (`.github/workflows/maintenance.yml`)
**Before**: 158 lines with repeated Python setup
**After**: 140 lines using composite action

**Changes**:
- Uses `setup-python` composite action for all Python setup steps
- Eliminates duplicate Python/UV installation code

#### 5. Release Workflow (`.github/workflows/release.yml`)
**Before**: 118 lines with repeated Python setup
**After**: 106 lines using composite action

**Changes**:
- Uses `setup-python` composite action for Python setup steps

### Benefits Achieved

#### 1. Code Reduction
- **Total lines reduced**: ~300 lines across all workflows
- **Duplication eliminated**: Python setup, Docker build, test execution

#### 2. Maintainability
- **Single source of truth**: Changes to common operations only need to be made once
- **Consistency**: All workflows use the same setup procedures
- **Version management**: Python and UV versions centrally managed

#### 3. Reusability
- **Cross-workflow sharing**: Components can be used across different workflows
- **Future workflows**: New workflows can leverage existing components
- **External usage**: Reusable workflows can be called from other repositories

#### 4. Error Reduction
- **Standardized processes**: Reduces chance of configuration drift
- **Centralized secrets**: Test environment setup is consistent
- **Validated patterns**: Reusable components are tested across multiple workflows

### Usage Examples

#### Using Reusable Test Workflow
```yaml
test:
  uses: ./.github/workflows/reusable-test.yml
  with:
    python-version: "3.12"
    upload-coverage: true
  secrets:
    BRAVE_SEARCH_API_KEY: ${{ secrets.BRAVE_SEARCH_API_KEY }}
    # ... other secrets
```

#### Using Python Setup Action
```yaml
- name: Setup Python environment
  uses: ./.github/actions/setup-python
  with:
    python-version: "3.12"
    install-dev: true
```

### Future Improvements

1. **Additional Reusable Components**:
   - Security scanning workflow
   - Performance testing workflow
   - Deployment notification workflow

2. **Enhanced Parameterization**:
   - Configurable test patterns
   - Optional code quality checks
   - Flexible Docker build contexts

3. **Cross-Repository Usage**:
   - Make workflows callable from other repositories
   - Create organization-wide workflow templates

---

## Troubleshooting

### Common Issues

#### 1. CI/CD Pipeline Issues

**Test Failures**:
- Check PostgreSQL service health
- Verify environment variables are set correctly
- Review test logs for specific error messages

**Docker Build Failures**:
- Verify Dockerfile syntax and dependencies
- Check if base images are accessible
- Ensure build context includes all required files

**Deployment Failures**:
- Check environment variables and network connectivity
- Verify SSH keys and credentials
- Review deployment logs for specific errors

#### 2. Docker Swarm Issues

**Services not starting**:
```bash
docker service logs codepluse-platform_agent_service
```

**Traefik routing issues**:
- Check if `traefik_main` network exists
- Verify domain DNS configuration
- Check Traefik dashboard for service discovery

**API connection issues**:
- Verify API keys are properly set
- Check service logs for authentication errors
- Ensure external API endpoints are accessible

#### 3. Docker Image Issues

**Build failures**:
- Ensure all build dependencies are installed
- Check that all required files are copied
- Verify multi-stage build steps

**Runtime errors**:
- Check that all required files are copied
- Verify non-root user has proper permissions
- Review environment variable settings

**Permission issues**:
- Verify non-root user has proper permissions
- Check file ownership in Docker image

### Debugging Commands

#### CI/CD Debugging
```bash
# Check GitHub Actions logs for detailed error messages
# Run tests locally to reproduce issues
python -m pytest tests/ -v

# Verify environment configuration
docker-compose config
```

#### Docker Swarm Debugging
```bash
# Check service health
docker service ls
docker service ps codepluse-platform_agent_service --no-trunc

# Monitor resource usage
docker stats $(docker ps --format "{{.Names}}" | grep codepluse-platform)
```

#### Docker Image Debugging
```bash
# Inspect image layers
docker history streamlit_app:optimized

# Check running container
docker exec -it streamlit-app sh

# View logs
docker logs streamlit-app
```

### Rolling Back

#### Service Rollback
```bash
# Rollback to previous version
docker service rollback codepluse-platform_agent_service
docker service rollback codepluse-platform_streamlit_app
```

#### Complete Stack Removal
```bash
docker stack rm codepluse-platform
```

### Getting Help

1. Check existing GitHub Issues
2. Create a new issue with the bug report template
3. Include relevant logs and environment information

---

## Best Practices

### Development Workflow

1. Create feature branches from `develop`
2. Open PR against `develop` for review
3. Merge to `main` (CI can be run manually if needed)
4. Create tags for production releases

### Commit Messages

Use conventional commit format:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation
- `ci:` for CI/CD changes

### Testing

- Write tests for new features
- Maintain code coverage above 80%
- Test Docker builds locally before pushing

### Security

#### Container Security
1. **Environment Variables**: Store sensitive data in Docker secrets
2. **Network Isolation**: Services use internal network for inter-service communication
3. **TLS**: All external traffic is encrypted via Traefik
4. **Resource Limits**: Prevent resource exhaustion with proper limits
5. **Health Checks**: Automatic service recovery on failures

#### CI/CD Security
- Use GitHub Secrets for sensitive data
- Environment-specific secrets in deployment environments
- Regular rotation of API keys and passwords
- Container scanning for vulnerabilities
- Dependency security audits

### Monitoring

#### Code Coverage
- Codecov integration for coverage reporting
- Coverage badges in README
- PR comments with coverage changes

#### Performance
- Basic performance tests in CI
- Weekly performance baseline establishment
- Docker image size monitoring

#### Resource Usage
- Monitor service resource consumption
- Set appropriate resource limits and reservations
- Regular performance audits

### Docker Best Practices Applied

1. **Dependency Management**: Only install what's needed
2. **Layer Caching**: Optimize Docker layer caching
3. **Security**: Run as non-root user
4. **Monitoring**: Built-in health checks
5. **Performance**: Optimized Python runtime settings
6. **Maintenance**: Clear documentation and CI integration

### Future Improvements

#### Docker Optimization
1. **Distroless images**: Consider Google's distroless Python images
2. **Static linking**: Explore static compilation for even smaller images
3. **Dependency analysis**: Regular review of dependency tree
4. **Compression**: Implement image compression in registry

#### CI/CD Enhancements
1. **Security scanning workflow**: Enhanced vulnerability detection
2. **Performance testing workflow**: Automated performance benchmarks
3. **Deployment notification workflow**: Improved deployment tracking
4. **Cross-repository templates**: Organization-wide workflow standards

#### Deployment Improvements
1. **Blue-green deployments**: Zero-downtime deployment strategy
2. **Canary releases**: Gradual rollout with monitoring
3. **A/B testing**: Feature flag integration
4. **Multi-region deployments**: Geographic distribution
