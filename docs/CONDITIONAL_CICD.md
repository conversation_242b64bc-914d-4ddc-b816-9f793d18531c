# Conditional CI/CD Build System

This document explains the conditional CI/CD build system that only builds Docker images when related code changes.

## Overview

The CI/CD system has been optimized to only build Docker images when the relevant code for each service has changed. This reduces build times, resource usage, and deployment overhead.

## Services and Their Dependencies

### Agent Service
**Docker Image**: `agent_service`
**Triggers when these files change**:
- `src/agents/**` - Agent implementations
- `src/core/**` - Core modules (LLM, settings)
- `src/memory/**` - Memory implementations
- `src/schema/**` - Protocol schemas
- `src/service/**` - FastAPI service code
- `src/run_service.py` - Service entry point
- `docker/Dockerfile.service` - Service Dockerfile
- `docker/start_service.py` - Service startup script
- `docker/service_init_patch.py` - Service patches
- `docker/core_init_patch.py` - Core patches
- `.dockerignore.service` - Service dockerignore
- `requirements.txt`, `pyproject.toml`, `uv.lock` - Dependencies

### Streamlit App
**Docker Image**: `streamlit_app`
**Triggers when these files change**:
- `src/client/**` - Agent client code
- `src/schema/**` - Protocol schemas (shared)
- `src/streamlit_app.py` - Streamlit app entry point
- `docker/Dockerfile.app` - App Dockerfile
- `.dockerignore.app` - App dockerignore
- `requirements.txt`, `pyproject.toml`, `uv.lock` - Dependencies

### Frontend
**Docker Image**: `frontend`
**Triggers when these files change**:
- `src/frontend/**` - All frontend code
- `docker/Dockerfile.frontend` - Frontend Dockerfile
- `docker/nginx.conf` - Nginx configuration
- `.dockerignore.frontend` - Frontend dockerignore

## Workflow Structure

### 1. Change Detection (`reusable-changes.yml`)
- Uses `dorny/paths-filter` to detect which files have changed
- Outputs boolean flags for each service: `agent_service`, `streamlit_app`, `frontend_docker`
- Also maintains legacy outputs for backward compatibility

### 2. Conditional Docker Build (`reusable-conditional-docker-build.yml`)
- Automatically detects changes and builds only affected services
- Uses the enhanced `reusable-docker-build.yml` with conditional matrix generation
- Returns outputs indicating which services were built

### 3. Enhanced Docker Build (`reusable-docker-build.yml`)
- Accepts parameters to control which services to build
- Generates dynamic build matrix based on input parameters
- Skips services that don't need to be built

## Usage in Workflows

### CI Pipeline (`ci.yml`)
```yaml
docker-build:
  uses: ./.github/workflows/reusable-conditional-docker-build.yml
  permissions:
    contents: read
  with:
    push: false
```

### CD Pipeline (`cd.yml`)
```yaml
build-and-push:
  uses: ./.github/workflows/reusable-conditional-docker-build.yml
  permissions:
    contents: read
    packages: write
  with:
    push: true
    registry: dockerhub.csharpp.com
    tag-prefix: ${{ needs.get-version.outputs.version }}
```

## Benefits

1. **Faster Builds**: Only builds what's needed
2. **Resource Efficiency**: Reduces CI/CD resource usage
3. **Faster Deployments**: Only deploys changed services
4. **Better Debugging**: Clear indication of what was built
5. **Cost Savings**: Reduced build minutes and registry storage

## Monitoring

The Portainer webhook notification now includes:
- Dynamic image list (only built images)
- `built_services` object indicating which services were built
- This allows deployment systems to only update changed services

## Backward Compatibility

The system maintains backward compatibility with existing workflows while adding the new conditional logic. Legacy change detection outputs are still available.
